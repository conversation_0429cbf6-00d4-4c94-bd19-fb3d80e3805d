@startuml
class RegisterEntrySearchView {
  UUID registerId
  String title
  String firstname
  String lastname
  String birthname
  String normalizedName
  String birthplace
  String normalizedBirthplace
  String birthdate
  String nationality
  String identificationNumber
  SearchItem data
}
class SearchItem {
  String registerId
  SearchItemPerson person
  Jurisdiction jurisdiction
  List<SearchItemFishingLicense> fishingLicenses
  List<SearchItemQualificationsProof> qualificationProofs
}
class SearchItemPerson {
  String title
  String firstname
  String lastname
  String birthdate
  String birthname
  String birthplace
}
class Jurisdiction {
  String federalState
}
class SearchItemFishingLicense {
  String validFrom;
  String number;
}
class SearchItemQualificationsProof {
  String fishinCertificateId
}

RegisterEntrySearchView *-- SearchItem

SearchItem *-- SearchItemPerson
SearchItem *-- Jurisdiction
SearchItem "1" *-- "many" SearchItemFishingLicense
SearchItem "1" *-- "many" SearchItemQualificationsProof
@enduml