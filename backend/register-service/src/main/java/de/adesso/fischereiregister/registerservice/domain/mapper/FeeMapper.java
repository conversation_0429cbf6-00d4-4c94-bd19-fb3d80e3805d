package de.adesso.fischereiregister.registerservice.domain.mapper;

import de.adesso.fischereiregister.core.model.Fee;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(
        nullValueIterableMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT
)
public interface FeeMapper {

    FeeMapper INSTANCE = Mappers.getMapper(FeeMapper.class);

    @Mapping(target = "federalState", expression = "java(dto.getFederalState().getValue())")
    Fee toFee(org.openapitools.model.Fee dto);

    List<Fee> toFees(List<org.openapitools.model.Fee> dtos);
}
