package de.adesso.fischereiregister.registerservice.identification_document_view;

import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import jakarta.transaction.Transactional;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.springframework.stereotype.Component;


@Component
@AllArgsConstructor
public class IdentificationDocumentViewHandler {

    private final IdentificationDocumentViewService identificationDocumentViewService;

    @ResetHandler
    @Transactional
    public void onReset() {
        identificationDocumentViewService.deleteAll();
    }

    @EventHandler
    @Transactional
    void on(RegularLicenseDigitizedEvent event) {
        identificationDocumentViewService.createIdentificationDocumentViews(event.registerId(), event.salt(), event.identificationDocuments());
    }

    @EventHandler
    @Transactional
    void on(PersonCreatedEvent event) {

        identificationDocumentViewService.createIdentificationDocumentViews(event.registerId(), event.salt(), event.identificationDocuments());
    }

    @EventHandler
    @Transactional
    void on(JurisdictionMovedEvent event) {

        identificationDocumentViewService.createIdentificationDocumentViews(event.registerId(), event.salt(), event.identificationDocuments());
    }

    @EventHandler
    @Transactional
    void on(FishingTaxPayedEvent event) {

        identificationDocumentViewService.createIdentificationDocumentViews(event.registerId(), event.salt(), event.identificationDocuments());
    }

    @EventHandler
    @Transactional
    void on(PersonalDataChangedEvent event) {
        identificationDocumentViewService.createIdentificationDocumentViews(event.registerId(), event.salt(), event.identificationDocuments());

    }

    @EventHandler
    @Transactional
    void on(RegularLicenseCreatedEvent event) {
        identificationDocumentViewService.createIdentificationDocumentViews(event.registerId(), event.salt(), event.identificationDocuments());

    }

    @EventHandler
    @Transactional
    void on(LimitedLicenseCreatedEvent event) {
        identificationDocumentViewService.createIdentificationDocumentViews(event.registerId(), event.salt(), event.identificationDocuments());

    }

    @EventHandler
    @Transactional
    void on(VacationLicenseCreatedEvent event) {
        identificationDocumentViewService.createIdentificationDocumentViews(event.registerEntryId(), event.salt(), event.identificationDocuments());
    }

    @EventHandler
    @Transactional
    void on(LicenseExtendedEvent event) {
        identificationDocumentViewService.createIdentificationDocumentViews(event.registerEntryId(), event.salt(), event.identificationDocuments());
    }

    @EventHandler
    @Transactional
    void on(ReplacementCardOrderedEvent event) {
        identificationDocumentViewService.createIdentificationDocumentViews(event.registerId(), event.salt(), event.identificationDocuments());

    }
}
