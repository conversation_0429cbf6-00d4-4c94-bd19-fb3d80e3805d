package de.adesso.fischereiregister.registerservice.statistics.taxes_statistics;

import de.adesso.fischereiregister.view.taxes_statistics.persistence.TaxesStatisticsView;

import java.util.List;

public interface TaxesStatisticsTransformationService {
    /**
     * Transforms a list of TaxesStatisticsView entities into a list of TaxesStatistics objects.
     * The transformation groups the statistics by year and source.
     *
     * @param statisticsViews The list of TaxesStatisticsView entities to transform.
     * @return A list of TaxesStatistics objects.
     */
    List<TaxesStatistics> transformToTaxesStatistics(List<TaxesStatisticsView> statisticsViews);
}
