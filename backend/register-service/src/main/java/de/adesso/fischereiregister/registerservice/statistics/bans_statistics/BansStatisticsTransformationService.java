package de.adesso.fischereiregister.registerservice.statistics.bans_statistics;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;

import java.util.List;

/**
 * Transforms {@link BansStatisticsView} objects into {@link BansStatistics} objects, grouped by year.
 */
public interface BansStatisticsTransformationService {

    /**
     * Converts a list of {@link BansStatisticsView} objects into a list of {@link BansStatistics} objects.
     *
     * @param statisticsViews The list of raw statistical data to transform.
     * @return A list of transformed {@link BansStatistics} objects.
     */
    List<BansStatistics> transformToBansStatistics(List<BansStatisticsView> statisticsViews);
}
