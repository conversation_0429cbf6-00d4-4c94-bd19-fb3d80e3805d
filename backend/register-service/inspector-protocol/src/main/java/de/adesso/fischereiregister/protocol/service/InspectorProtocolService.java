package de.adesso.fischereiregister.protocol.service;

import de.adesso.fischereiregister.core.ports.InspectorProtocolServicePort;

import java.time.LocalDateTime;

/**
 * This interface defines the contract for the Inspector Protocol Service.
 * It is a duplicate of the InspectorProtocolServicePort interface, and its there for testing purposes.
 */
public interface InspectorProtocolService extends InspectorProtocolServicePort {

    /**
     * Creates a new protocol entry for the given inspector.
     *
     * @param inspectorUserId      The ID of the inspector.
     * @param registerEntryId      The ID of the register entry. (can be null)
     * @param inspectorFederalState The federal state of the inspector.
     * @param inspectionTimestamp  The timestamp of the inspection. (is not null, has to be set by the caller because of possible time zone issues)
     *                             Also the logic of when the inspection took place is not in the scope of this service.??
     */
    public void createProtocolEntry(String inspectorUserId, String registerEntryId, String inspectorFederalState, LocalDateTime inspectionTimestamp);
}
