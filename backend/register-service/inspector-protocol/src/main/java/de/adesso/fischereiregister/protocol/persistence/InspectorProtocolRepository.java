package de.adesso.fischereiregister.protocol.persistence;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.UUID;

/**
 * Repository interface for managing Protocol entities.
 * This interface extends CrudRepository to provide basic CRUD operations.
 */
@Component
@Repository
public interface InspectorProtocolRepository extends CrudRepository<InspectorProtocol, UUID> {

}
