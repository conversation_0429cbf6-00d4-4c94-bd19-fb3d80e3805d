group = 'de.adesso.fischereiregister'
version = parent.project.version

dependencies {
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'
    compileOnly 'org.projectlombok:lombok:1.18.36'
    annotationProcessor 'org.projectlombok:lombok:1.18.36'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'

    // Only include Spring annotations
    implementation 'org.springframework:spring-context:6.2.0'

    configurations.all {
        exclude group: 'org.springframework.boot'
        exclude module: 'spring-boot'
        exclude module: 'spring-boot-starter'
        exclude module: 'spring-web'
    }

    api "org.axonframework:axon-spring-boot-starter:4.10.3"
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    // https://mvnrepository.com/artifact/org.mockito/mockito-core
    testImplementation group: 'org.mockito', name: 'mockito-core', version: '2.1.0'
    testImplementation 'org.mockito:mockito-junit-jupiter'


    api platform('org.axonframework:axon-bom:4.10.0')
    api "org.axonframework:axon-spring"
    testImplementation "org.axonframework:axon-test"
    testImplementation 'org.hamcrest:hamcrest:2.2'

    implementation('com.fasterxml.jackson.core:jackson-annotations:2.15.4')
    constraints {
        implementation('com.thoughtworks.xstream:xstream:1.4.21') {
            because "CVE-2024-47072"
        }
    }
}