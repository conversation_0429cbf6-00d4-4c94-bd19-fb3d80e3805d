package de.adesso.fischereiregister.view.bans_statistics.services;

import de.adesso.fischereiregister.view.bans_statistics.persistance.BansStatisticsView;
import de.adesso.fischereiregister.view.bans_statistics.persistance.InMemoryBansStatisticsViewRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class BansStatisticsViewServiceImplTest {

    private InMemoryBansStatisticsViewRepository repository;
    private BansStatisticsViewService service;

    @BeforeEach
    void setUp() {
        repository = new InMemoryBansStatisticsViewRepository();
        service = new BansStatisticsViewServiceImpl(repository);
        repository.deleteAll(); // Ensure clean state before each test

        // Setup common test data for all tests
        // Create test data for SH 2022
        createStatistic(1L, "SH", 2022, 10, 5);

        // Create test data for SH 2023
        createStatistic(2L, "SH", 2023, 15, 8);

        // Create test data for BE 2022
        createStatistic(3L, "BE", 2022, 7, 3);

        // Create test data for BE 2023
        createStatistic(4L, "BE", 2023, 12, 6);
    }

    @Test
    @DisplayName("Should create new statistic when none exists for issued ban")
    void testCreateNewIssuedStatistic() {
        // Given
        repository.deleteAll(); // Start with clean repository for this test
        String federalState = "SH";
        int year = 2023;

        // When
        service.updateOrCreateIssuedStatistic(federalState, year);

        // Then
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(
                federalState, year);

        assertTrue(result.isPresent());
        assertEquals(1, result.get().getIssuedCount());
        assertEquals(0, result.get().getExpiredCount());
        assertEquals(federalState, result.get().getFederalState());
        assertEquals(year, result.get().getYear());
    }

    @Test
    @DisplayName("Should create new statistic when none exists for expired ban")
    void testCreateNewExpiredStatistic() {
        // Given
        repository.deleteAll(); // Start with clean repository for this test
        String federalState = "SH";
        int year = 2023;

        // When
        service.updateOrCreateExpiredStatistic(federalState, year);

        // Then
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(
                federalState, year);

        assertTrue(result.isPresent());
        assertEquals(0, result.get().getIssuedCount());
        assertEquals(1, result.get().getExpiredCount());
        assertEquals(federalState, result.get().getFederalState());
        assertEquals(year, result.get().getYear());
    }

    @Test
    @DisplayName("Should update existing statistic when one exists for issued ban")
    void testUpdateExistingIssuedStatistic() {
        // Given
        repository.deleteAll(); // Start with clean repository for this test
        String federalState = "SH";
        int year = 2023;

        // Create initial statistic
        createStatistic(null, federalState, year, 5, 3);

        // When
        service.updateOrCreateIssuedStatistic(federalState, year);

        // Then
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(
                federalState, year);

        assertTrue(result.isPresent());
        assertEquals(6, result.get().getIssuedCount()); // IssuedCount should be incremented
        assertEquals(3, result.get().getExpiredCount()); // ExpiredCount should remain the same
    }

    @Test
    @DisplayName("Should update existing statistic when one exists for expired ban")
    void testUpdateExistingExpiredStatistic() {
        // Given
        repository.deleteAll(); // Start with clean repository for this test
        String federalState = "SH";
        int year = 2023;

        // Create initial statistic
        createStatistic(null, federalState, year, 5, 3);

        // When
        service.updateOrCreateExpiredStatistic(federalState, year);

        // Then
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(
                federalState, year);

        assertTrue(result.isPresent());
        assertEquals(5, result.get().getIssuedCount()); // IssuedCount should remain the same
        assertEquals(4, result.get().getExpiredCount()); // ExpiredCount should be incremented
    }

    @Test
    @DisplayName("Should get statistics by federal state and years")
    void testGetStatisticsByFederalStateAndYears() {
        // When
        List<BansStatisticsView> results = service.getStatisticsByFederalStateAndYears(
                "SH", Arrays.asList(2022, 2023));

        // Then
        assertEquals(2, results.size());
        assertTrue(results.stream().allMatch(view -> view.getFederalState().equals("SH")));
    }

    @Test
    @DisplayName("Should get statistics by years")
    void testGetStatisticsByYears() {
        // When
        List<BansStatisticsView> results = service.getStatisticsByYears(
                Arrays.asList(2022, 2023));

        // Then
        assertEquals(4, results.size());
    }

    @Test
    @DisplayName("Should get available years")
    void testGetAvailableYears() {
        // When
        List<Integer> years = service.getAvailableYears();

        // Then
        assertEquals(2, years.size());
        assertTrue(years.contains(2022));
        assertTrue(years.contains(2023));
    }

    @Test
    @DisplayName("Should delete all statistics")
    void testDeleteAll() {
        // When
        service.deleteAll();

        // Then
        List<Integer> years = service.getAvailableYears();
        assertEquals(0, years.size());
    }

    @Test
    @DisplayName("Should decrease issued count when decreaseIssuedStatistic is called")
    void testDecreaseIssuedStatistic() {
        // Given
        String federalState = "SH";
        int year = 2022;

        // Initial count is 10 (from setUp)
        Optional<BansStatisticsView> initialView = repository.findByFederalStateAndYear(federalState, year);
        assertTrue(initialView.isPresent());
        assertEquals(10, initialView.get().getIssuedCount());

        // When
        service.decreaseIssuedStatistic(federalState, year);

        // Then
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(federalState, year);
        assertTrue(result.isPresent());
        assertEquals(9, result.get().getIssuedCount()); // Should be decremented by 1
        assertEquals(5, result.get().getExpiredCount()); // Should remain unchanged
    }

    @Test
    @DisplayName("Should set issued count to 0 when decreasing to 0")
    void testDecreaseIssuedStatisticToZero() {
        // Given
        repository.deleteAll();
        String federalState = "SH";
        int year = 2022;

        // Create a statistic with issued count of 1 and expired count of 5
        createStatistic(null, federalState, year, 1, 5);

        // When
        service.decreaseIssuedStatistic(federalState, year);

        // Then
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(federalState, year);
        assertTrue(result.isPresent());
        assertEquals(0, result.get().getIssuedCount()); // Should be set to 0
        assertEquals(5, result.get().getExpiredCount()); // Should remain unchanged
    }

    @Test
    @DisplayName("Should keep entry with zero counts")
    void testKeepEntryWithZeroCounts() {
        // Given
        repository.deleteAll();
        String federalState = "SH";
        int year = 2022;

        // Create a statistic with issued count of 1 and expired count of 0
        createStatistic(null, federalState, year, 1, 0);

        // When
        service.decreaseIssuedStatistic(federalState, year);

        // Then
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(federalState, year);
        assertTrue(result.isPresent()); // Entry should be kept
        assertEquals(0, result.get().getIssuedCount()); // Should be set to 0
        assertEquals(0, result.get().getExpiredCount()); // Should remain 0
    }

    @Test
    @DisplayName("Should keep entry with zero counts when decreasing started count to zero")
    void testKeepEntryWithZeroStartedCount() {
        // Given
        repository.deleteAll();
        String federalState = "SH";
        int year = 2022;

        // Create a statistic with started count of 1 and other counts at 0
        BansStatisticsView view = new BansStatisticsView();
        view.setFederalState(federalState);
        view.setYear(year);
        view.setIssuedCount(0);
        view.setExpiredCount(0);
        view.setStartedCount(1);
        repository.save(view);

        // When
        service.decreaseStartedStatistic(federalState, year);

        // Then
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(federalState, year);
        assertTrue(result.isPresent()); // Entry should be kept
        assertEquals(0, result.get().getIssuedCount()); // Should remain 0
        assertEquals(0, result.get().getExpiredCount()); // Should remain 0
        assertEquals(0, result.get().getStartedCount()); // Should be set to 0
    }

    @Test
    @DisplayName("Should do nothing when no statistic exists for the given parameters")
    void testDecreaseNonExistentStatistic() {
        // Given
        String federalState = "HH"; // Non-existent federal state
        int year = 2022;

        // When
        service.decreaseIssuedStatistic(federalState, year);

        // Then - no exception should be thrown
        Optional<BansStatisticsView> result = repository.findByFederalStateAndYear(federalState, year);
        assertTrue(result.isEmpty()); // Should still not exist
    }

    /**
     * Helper method to create a single statistic entry
     */
    private void createStatistic(Long id, String federalState, int year, int issuedCount, int expiredCount) {
        BansStatisticsView view = new BansStatisticsView();
        view.setId(id);
        view.setFederalState(federalState);
        view.setYear(year);
        view.setIssuedCount(issuedCount);
        view.setExpiredCount(expiredCount);
        repository.save(view);
    }
}
