package de.adesso.fischereiregister.view.licenses_statistics.services;

import de.adesso.fischereiregister.core.model.type.LicenseType;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsView;
import de.adesso.fischereiregister.view.licenses_statistics.persistance.LicensesStatisticsViewRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class LicensesStatisticsViewServiceImpl implements LicensesStatisticsViewService {

    private final LicensesStatisticsViewRepository repository;

    @Override
    public void deleteAll() {
        repository.deleteAll();
    }

    @Override
    public void updateOrCreateStatistic(LicenseType licenseType, String federalState, String office, SubmissionType source, int year) {
        // Try to find an existing entry
        LicensesStatisticsView view = repository.findByLicenseTypeAndFederalStateAndOfficeAndSourceAndYear(licenseType, federalState, office, source, year)
                .orElse(null);

        if (view != null) {
            view.setCount(view.getCount() + 1);
        } else {
            view = new LicensesStatisticsView();
            view.setFederalState(federalState);
            view.setOffice(office);
            view.setSource(source);
            view.setLicenseType(licenseType);
            view.setYear(year);
            view.setCount(1);
        }

        repository.save(view);
    }

    @Override
    public List<LicensesStatisticsView> getStatisticsByLicenseTypeAndFederalStateAndYears(LicenseType licenseType, String federalState, List<Integer> years) {
        return repository.findByLicenseTypeAndFederalStateAndYearIn(licenseType, federalState, years);
    }

    @Override
    public List<LicensesStatisticsView> getStatisticsByLicenseTypeAndOfficeAndYears(LicenseType licenseType, String office, List<Integer> years) {
        return repository.findByLicenseTypeAndOfficeAndYearIn(licenseType, office, years);
    }

    @Override
    public List<LicensesStatisticsView> getStatisticsByLicenseTypeAndYears(LicenseType licenseType, List<Integer> years) {
        return repository.findByLicenseTypeAndYearIn(licenseType, years);
    }


    @Override
    public List<Integer> getAvailableYears() {
        return repository.findDistinctYears();
    }
}
