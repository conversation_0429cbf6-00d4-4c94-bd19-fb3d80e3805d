<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd"
                   context="ban_expiration_view">

    <!-- ChangeSet for BanExpirationView Table -->
    <changeSet id="1.0.0" author="paul.lindt">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="ban_expiration_view"/>
            </not>
        </preConditions>
        <createTable tableName="ban_expiration_view">

            <column name="register_entry_id" type="uuid">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="expiration_date" type="datetime(6)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1.0.0-index" author="paul.lindt">
        <!-- Create index on expiration_date -->
        <preConditions onFail="MARK_RAN">
            <not>
                <indexExists indexName="idx_ban_expiration_expiration_date" tableName="ban_expiration_view"/>
            </not>
        </preConditions>
        <createIndex indexName="idx_ban_expiration_expiration_date" tableName="ban_expiration_view">
            <column name="expiration_date"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
