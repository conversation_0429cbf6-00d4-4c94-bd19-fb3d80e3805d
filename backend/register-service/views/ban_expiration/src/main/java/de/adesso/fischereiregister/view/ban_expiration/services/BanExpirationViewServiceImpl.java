package de.adesso.fischereiregister.view.ban_expiration.services;


import de.adesso.fischereiregister.view.ban_expiration.persistance.BanExpirationView;
import de.adesso.fischereiregister.view.ban_expiration.persistance.BanExpirationViewRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.UUID;

@Service
@AllArgsConstructor
class BanExpirationViewServiceImpl implements BanExpirationViewService {

    private final BanExpirationViewRepository repository;

    public void deleteAll() {
        repository.deleteAll();
    }

    public void setBanExpiration(UUID registerEntryId, LocalDate expirationDate) {
        final BanExpirationView banExpirationView = new BanExpirationView();
        banExpirationView.setRegisterEntryId(registerEntryId);
        banExpirationView.setExpirationDate(expirationDate);
        repository.save(banExpirationView);

    }

    public void deleteBanExpiration(UUID registerEntryId) {
        repository.deleteById(registerEntryId);
    }


}
