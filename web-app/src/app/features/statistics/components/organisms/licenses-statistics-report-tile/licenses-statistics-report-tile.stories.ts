import { Meta, StoryObj, argsToTemplate } from '@storybook/angular';

import { LicenseType } from '@digifischdok/ngx-register-sdk';

import { LicensesStatisticsReportTileComponent } from '@/app/features/statistics/components/organisms/licenses-statistics-report-tile/licenses-statistics-report-tile.component';

const actualYear = new Date().getFullYear();

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<LicensesStatisticsReportTileComponent> = {
  title: 'statistics/LicensesStatisticsReportTile',
  component: LicensesStatisticsReportTileComponent,
  render: (args) => ({
    props: { ...args },
    template: `<div class="flex justify-center">
                  <div class="w-[460px]">
                    <fish-licenses-statistics-report-tile ${argsToTemplate(args)}> </fish-licenses-statistics-report-tile> 
                  </div>
               </div>`,
  }),
  args: {
    statistics: [
      {
        year: actualYear - 5,
        data: [
          { submissionType: 'ANALOG', count: 25000 },
          { submissionType: 'ONLINE', count: 7000 },
        ],
      },
      {
        year: actualYear - 4,
        data: [
          { submissionType: 'ANALOG', count: 30000 },
          { submissionType: 'ONLINE', count: 14000 },
        ],
      },
      {
        year: actualYear - 3,
        data: [
          { submissionType: 'ANALOG', count: 27000 },
          { submissionType: 'ONLINE', count: 10000 },
        ],
      },
      {
        year: actualYear - 2,
        data: [
          { submissionType: 'ANALOG', count: 32000 },
          { submissionType: 'ONLINE', count: 14000 },
        ],
      },
      {
        year: actualYear - 1,
        data: [
          { submissionType: 'ANALOG', count: 22000 },
          { submissionType: 'ONLINE', count: 11000 },
        ],
      },
      {
        year: actualYear,
        data: [
          { submissionType: 'ANALOG', count: 8000 },
          { submissionType: 'ONLINE', count: 1000 },
        ],
      },
    ],
  },
};

export default meta;

type Story = StoryObj<LicensesStatisticsReportTileComponent>;

export const Regular: Story = {};

export const Vacation: Story = {
  args: {
    licenseType: LicenseType.Vacation,
  },
};

export const Limited: Story = {
  args: {
    licenseType: LicenseType.Limited,
  },
};
