import { DatePipe, KeyValue, KeyValuePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, InputSignal, WritableSignal, inject, input, signal } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { KeycloakService } from 'keycloak-angular';

import { LoadingOverlayComponent } from '@/app/core/layout/loading-overlay/loading-overlay.component';
import { CreatePassedExamSummaryPipe } from '@/app/features/create-passed-exam/components/organisms/create-passed-exam-summary/create-passed-exam-summary.pipe';
import { CreatePassedExamValues } from '@/app/features/create-passed-exam/models/create-passed-exam.models';
import { HighlightSearchResultPipe } from '@/app/features/search/components/molecules/search-table-row/pipes/highlight-search-result.pipe';
import { SearchTableRowComponent } from '@/app/features/search/components/molecules/search-table-row/search-table-row.component';
import { InspectionLinkComponent } from '@/app/shared/atoms/inspection-link/inspection-link.component';
import { LabelComponent } from '@/app/shared/atoms/label/label.component';
import { ListComponent } from '@/app/shared/atoms/list/list.component';
import { TableCellComponent } from '@/app/shared/atoms/table-cell/table-cell.component';
import { TableHeaderComponent } from '@/app/shared/atoms/table-header/table-header.component';
import { TableRowComponent } from '@/app/shared/atoms/table-row/table-row.component';
import { BeforeUnloadAndCanDeactivateBase } from '@/app/shared/decorators/before-unload-and-can-deactivate/before-unload-and-can-deactivate.class';
import { FishBeforeUnloadAndCanDeactivate } from '@/app/shared/decorators/before-unload-and-can-deactivate/before-unload-and-can-deactivate.decorator';

export type GeneratorKeyValue = Generator<KeyValue<string, string | null | unknown>, void>;

@FishBeforeUnloadAndCanDeactivate()
@Component({
  selector: 'fish-create-passed-exam-summary',
  standalone: true,
  imports: [
    LabelComponent,
    ListComponent,
    SearchTableRowComponent,
    TableCellComponent,
    TableHeaderComponent,
    TableRowComponent,
    HighlightSearchResultPipe,
    InspectionLinkComponent,
    LoadingOverlayComponent,
    TranslateModule,
    DatePipe,
    CreatePassedExamSummaryPipe,
  ],
  providers: [KeyValuePipe],
  templateUrl: './create-passed-exam-summary.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CreatePassedExamSummaryComponent extends BeforeUnloadAndCanDeactivateBase implements AfterViewInit {
  public readonly data: InputSignal<CreatePassedExamValues> = input.required();

  protected readonly examinationFacility: WritableSignal<string> = signal('');

  protected readonly entriesGenerator: GeneratorKeyValue = this.generatePassedExamValues();

  private readonly keyValuePipe: KeyValuePipe = inject(KeyValuePipe);

  private readonly keycloakService: KeycloakService = inject(KeycloakService);

  constructor() {
    super();
    this.examinationFacility.set(this.keycloakService.getKeycloakInstance().idTokenParsed?.['examination']?.issuer ?? '');
  }

  public ngAfterViewInit(): void {
    this.setHasUnsavedChanges(true);
  }

  /**
   * Generates a sequence of key-value pairs for the passed exam values.
   *
   * This method uses a generator to yield key-value pairs transformed by a custom pipe.
   * It iterates over the data provided by the `data()` method and transforms it using the `keyValuePipe`.
   *
   * @generator
   * @yields {GeneratorKeyValue} A key-value pair from the transformed data.
   */
  private *generatePassedExamValues(): GeneratorKeyValue {
    for (const keyValue of this.keyValuePipe.transform(this.data(), (): 0 => 0)) {
      yield keyValue;
    }
  }
}
