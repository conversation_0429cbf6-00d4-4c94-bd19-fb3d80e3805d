import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';

import { FederalStateAbbreviation, LicenseType } from '@digifischdok/ngx-register-sdk';

import { LicenseCardItemType } from '@/app/features/service-overview/components/organisms/license-service-card/license-service-card.models';
import { BadgeComponent } from '@/app/shared/atoms/badge/badge.component';
import { IconAnyProofComponent } from '@/app/shared/icons/any-proof/any-proof.component';
import { IconHandicappedComponent } from '@/app/shared/icons/handicapped/handicapped.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';
import { IconVacationComponent } from '@/app/shared/icons/vacation/vacation.component';
import { DocumentNumberPipe } from '@/app/shared/pipes/document-number.pipe';
import { NamingsService } from '@/app/shared/services/namings/namings.service';
import { toComputed } from '@/app/shared/utils/rxJsInterop.utils';

@Component({
  selector: 'fish-license-card-item-title',
  standalone: true,
  imports: [
    TranslateModule,
    DocumentNumberPipe,
    BadgeComponent,
    IconAnyProofComponent,
    IconHandicappedComponent,
    IconVacationComponent,
    IconLicenseCardComponent,
  ],
  templateUrl: './license-card-item-title.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LicenseCardItemTitleComponent {
  public readonly type = input.required<LicenseCardItemType>();

  public readonly identificationNumber = input<string | undefined>();

  public readonly errorLabelText = input<string | undefined>();

  public readonly issuingFederalState = input<FederalStateAbbreviation | undefined>();

  protected readonly LicenseType = LicenseType;

  protected readonly LicenseCardItemType = LicenseCardItemType;

  protected readonly licenseName = toComputed(() => {
    const issuingFederalState = this.issuingFederalState();
    if (!issuingFederalState) {
      throw new Error('Corrupted data detected. License has no issuing federal state set.');
    }

    const type = this.type();

    if (type === LicenseCardItemType.Certificate) {
      return of('');
    }

    return this.namingsService.getLicenseNaming$(issuingFederalState, type as LicenseType);
  });

  // DEPENDENCIES
  private readonly namingsService = inject(NamingsService);
}
