import { ChangeDetectionStrategy, Component, OnInit, Signal, computed, inject } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { Person } from '@digifischdok/ngx-register-sdk';

import { CitizenStore } from '@/app/core/stores/citizen.store';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { NoticeActionsComponent } from '@/app/shared/atoms/notice-actions/notice-actions.component';
import { NoticeContentComponent } from '@/app/shared/atoms/notice-content/notice-content.component';
import { IconMoveAuthorityComponent } from '@/app/shared/icons/move-authority/move-authority.component';
import { FederalState } from '@/app/shared/models/federal-state';
import { NoticeComponent } from '@/app/shared/molecules/notice/notice.component';
import { PersonFullnamePipe } from '@/app/shared/pipes/person-fullname.pipe';
import { DataCatalogService } from '@/app/shared/services/data-catalog.service';

@Component({
  selector: 'fish-jurisdiction-notice',
  standalone: true,
  imports: [
    ButtonComponent,
    IconMoveAuthorityComponent,
    NoticeActionsComponent,
    NoticeComponent,
    NoticeContentComponent,
    PersonFullnamePipe,
    TranslateModule,
  ],
  templateUrl: './jurisdiction-notice.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class JurisdictionNoticeComponent implements OnInit {
  protected readonly actualJurisdiction: Signal<string | undefined> = computed(() => {
    return this.federalStates.find((federalState) => federalState.id === this.citizenStore.profile()?.jurisdiction?.federalState)?.name;
  });

  protected readonly citizen: Signal<Person | undefined> = computed(() => {
    return this.citizenStore.profile()?.person;
  });

  private federalStates: FederalState[] = [];

  // Dependencies
  private readonly dataCatalogService: DataCatalogService = inject(DataCatalogService);

  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  public ngOnInit(): void {
    this.dataCatalogService.getFederalStates$().subscribe((federalStates) => (this.federalStates = federalStates));
  }
}
