<fish-notice type="info">
  <fish-notice-content>
    <div class="py-1 text-s">
      @if (actualJurisdiction()) {
        <div class="text-s" [innerHtml]="'jurisdiction_notice.text' | translate: { citizenFullname: citizen() | personFullname }"></div>
        <div class="text-base font-thin" [innerText]="actualJurisdiction()"></div>
      } @else {
        <!--        if actual jurisdiction is null, that means the register entry hat no assigned jurisdiction -->
        <div
          class="text-s"
          [innerHtml]="'jurisdiction_notice.text_no_jurisdiction' | translate: { citizenFullname: citizen() | personFullname }"
        ></div>
      }
    </div>
  </fish-notice-content>
  <fish-notice-actions>
    <div class="mx-3 flex items-center">
      <fish-button type="secondary" [routeTo]="'move-jurisdiction'" data-testid="service-overview-jurisdiction-notice-move-button">
        <fish-icon-move-authority size="32" icon></fish-icon-move-authority>
        @if (actualJurisdiction()) {
          <span [innerText]="'jurisdiction_notice.button.move_jurisdiction' | translate"></span>
        } @else {
          <span [innerText]="'jurisdiction_notice.button.set_jurisdiction' | translate"></span>
        }
      </fish-button>
    </div>
  </fish-notice-actions>
</fish-notice>
