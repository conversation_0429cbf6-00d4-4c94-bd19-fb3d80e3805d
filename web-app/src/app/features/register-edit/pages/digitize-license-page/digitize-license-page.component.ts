import { ChangeDetectionStrategy, Component, inject } from '@angular/core';

import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { BackgroundGradientComponent } from '@/app/core/layout/background-gradient/background-gradient.component';
import { BackgroundGraphicComponent } from '@/app/core/layout/background-graphic/background-graphic.component';
import { HeaderComponent } from '@/app/core/layout/header/header.component';
import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { ProfileHeaderComponent } from '@/app/core/layout/profile-header/profile-header.component';
import { ProfileHeaderStore } from '@/app/core/stores/profile-header.store';
import { DigitizeTabGroupComponent } from '@/app/features/register-edit/components/templates/digitize-tab-group/digitize-tab-group.component';

@Component({
  selector: 'fish-digitize-license-page',
  standalone: true,
  imports: [
    ProfileHeaderComponent,
    PageContentComponent,
    DigitizeTabGroupComponent,
    TranslateModule,
    BackgroundGraphicComponent,
    Header<PERSON>omponent,
    BackgroundGradientComponent,
  ],
  templateUrl: './digitize-license-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DigitizeLicensePageComponent {
  private readonly profileHeaderStore: ProfileHeaderStore = inject(ProfileHeaderStore);

  private readonly translate: TranslateService = inject(TranslateService);

  constructor() {
    this.translate.get('digitize.title').subscribe((text: string): void => this.profileHeaderStore.setText(text));
  }
}
