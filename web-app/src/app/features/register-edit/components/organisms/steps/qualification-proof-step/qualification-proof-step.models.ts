import { FormControl, FormGroup } from '@angular/forms';

import { CertificateFormGroup } from '@/app/features/register-edit/components/organisms/certificate-form/certificate-form.model';
import { LicenseFormGroup } from '@/app/features/register-edit/components/organisms/license-form/license-form.models';
import { OtherQualificationsProofFormGroup } from '@/app/features/register-edit/components/organisms/other-qualifications-proof-form/other-qualifications-proof-form.model';

export type QualificationProofFormGroup = FormGroup<{
  license: LicenseFormGroup;
  certificate: CertificateFormGroup;
  otherQualificationProof: OtherQualificationsProofFormGroup;
  selectedQualification: FormControl<'license' | 'certificate' | 'otherQualificationProof'>;
}>;

export type QualificationProofFormValues = QualificationProofFormGroup['value'];
