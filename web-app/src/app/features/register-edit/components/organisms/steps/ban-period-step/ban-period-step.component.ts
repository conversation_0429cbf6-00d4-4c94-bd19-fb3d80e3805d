import { AfterViewInit, ChangeDetectionStrategy, Component, OutputEmitterRef, ViewChild, effect, inject, input, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, distinctUntilChanged } from 'rxjs';

import { CitizenStore } from '@/app/core/stores/citizen.store';
import { BanPeriodFormComponent } from '@/app/features/register-edit/components/organisms/ban-period-form/ban-period-form.component';
import { BanPeriodFormGroup } from '@/app/features/register-edit/components/organisms/ban-period-form/ban-period-form.models';
import { EditFooterComponent } from '@/app/features/register-edit/components/organisms/edit-footer/edit-footer.component';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { CardComponent } from '@/app/shared/molecules/card/card.component';

@Component({
  selector: 'fish-ban-period-step',
  standalone: true,
  imports: [CardComponent, CardContentComponent, CardHeaderComponent, EditFooterComponent, BanPeriodFormComponent, TranslateModule],
  templateUrl: './ban-period-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BanPeriodStepComponent implements AfterViewInit, EditFormStep<BanPeriodFormGroup> {
  // Fields
  public readonly isLoading = input<boolean>(false);

  public readonly executeButtonClicked: OutputEmitterRef<void> = output<void>();

  public readonly backButtonClicked: OutputEmitterRef<void> = output<void>();

  public formGroup!: BanPeriodFormGroup;

  public canContinue$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  @ViewChild(BanPeriodFormComponent) private readonly banPeriodForm!: BanPeriodFormComponent;

  // Dependencies
  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  constructor() {
    this.initCitizenStoreEffect();
  }

  public ngAfterViewInit(): void {
    this.formGroup = this.banPeriodForm.formGroup;
    this.formGroup.statusChanges.pipe(distinctUntilChanged()).subscribe(() => this.canContinue$.next(this.formGroup.valid));
  }

  private initCitizenStoreEffect(): void {
    effect(() => {
      const { ban } = this.citizenStore.profile() ?? {};
      if (ban) {
        if (ban.to) {
          this.formGroup.patchValue({ type: 'temporary', temporary: { ...ban } });
        } else {
          this.formGroup.patchValue({ type: 'permanent', permanent: { ...ban } });
        }
        this.banPeriodForm.captureInitialState();
        this.formGroup.updateValueAndValidity();
      }
    });
  }

  protected onContinue(): void {
    this.banPeriodForm.validate();
    if (this.formGroup.valid) {
      this.executeButtonClicked.emit();
    }
  }
}
