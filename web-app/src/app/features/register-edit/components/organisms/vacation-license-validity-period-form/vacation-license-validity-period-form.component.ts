import { formatDate } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnInit, computed, inject, input, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormBuilder, ReactiveFormsModule, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { Observable, combineLatest, filter, forkJoin, map, startWith, switchMap } from 'rxjs';

import { FishingLicense, LicenseInformation, LicenseType, ValidityPeriod } from '@digifischdok/ngx-register-sdk';

import { UserService } from '@/app/core/services/user/user.service';
import { LicenseInformationStore } from '@/app/core/stores/license-information.store';
import { VacationLicenseValidityPeriodStepFormGroup } from '@/app/features/register-edit/components/organisms/steps/vacation-license-validity-period-step/vacation-license-validity-period-step.models';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { FormFieldComponent } from '@/app/shared/organisms/form-field/form-field.component';
import { ValidationErrorMapping } from '@/app/shared/organisms/form-field/form-field.models';
import { LicenseConfigurationService } from '@/app/shared/services/license-configuration.service';
import { DateValidators } from '@/app/shared/validators/date.validators';

@Component({
  selector: 'fish-vacation-license-validity-period-form',
  standalone: true,
  imports: [FormFieldComponent, TranslateModule, ReactiveFormsModule],
  templateUrl: './vacation-license-validity-period-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VacationLicenseValidityPeriodFormComponent extends FormComponent<VacationLicenseValidityPeriodStepFormGroup> implements OnInit {
  // INPUTS
  public readonly vacationLicenseToExtend = input<FishingLicense>(); // for a vacation license extension instead of creation

  public readonly vacationLicenses = input<FishingLicense[]>(); // to check that new vacation licenses do not overlap with the already existing one

  // FIELDS
  public override formGroup!: VacationLicenseValidityPeriodStepFormGroup;

  private readonly licenseConfig = signal<LicenseInformation | undefined>(undefined);

  protected readonly durationOptions = computed<string[]>(() => {
    const durationOptions = this.licenseConfig()?.durationOptions.map((option) => option.toString());
    return durationOptions ?? [];
  });

  protected readonly latestIssueDate = computed<Date>(() => {
    const date = this.licenseConfig()?.latestPossibleIssueDate;
    return date ? new Date(date) : new Date();
  });

  protected readonly previousValidityPeriod = computed<ValidityPeriod | undefined>(() => this.vacationLicenseToExtend()?.validityPeriods[0]);

  protected readonly otherVacationLicensesInSameJurisdiction = computed<FishingLicense[] | undefined>(() =>
    this.vacationLicenses()?.filter((license) => {
      // if there is a license to extend dont consider it for the vacation licenses to compare with
      const licenseToExtend = this.vacationLicenseToExtend();
      return license.issuingFederalState === this.userService.getFederalState() && (!licenseToExtend || license.number !== licenseToExtend.number);
    })
  );

  protected readonly durationOptions$ = toObservable(this.durationOptions);

  protected readonly latestIssueDate$ = toObservable(this.latestIssueDate);

  protected readonly previousValidityPeriod$ = toObservable(this.previousValidityPeriod);

  protected readonly otherVacationLicensesInSameJurisdiction$ = toObservable(this.otherVacationLicensesInSameJurisdiction);

  protected validFromErrorMapping$!: Observable<ValidationErrorMapping>;

  // DEPENDENCIES
  private readonly formBuilder = inject(FormBuilder);

  private readonly licenseInformationStore = inject(LicenseInformationStore);

  private readonly licenseConfigurationService = inject(LicenseConfigurationService);

  private readonly userService: UserService = inject(UserService);

  public override ngOnInit(): void {
    this.initLicenseConfiguration();
    this.initFormGroup();
  }

  private initLicenseConfiguration() {
    const licenseConfiguration = this.licenseInformationStore.getLicenseConfiguration(LicenseType.Vacation);
    this.licenseConfig.set(licenseConfiguration);
  }

  private initFormGroup() {
    this.formGroup = this.formBuilder.group({
      duration: this.formBuilder.nonNullable.control<string>('', [Validators.required]),
      validFrom: this.formBuilder.control<string | null>(null),
      validTo: this.formBuilder.control<string | null>({
        value: null,
        disabled: true,
      }),
    });
    const validFromControl = this.formGroup.controls.validFrom;
    const durationControl = this.formGroup.controls.duration;

    combineLatest([
      validFromControl.valueChanges.pipe(startWith(validFromControl.value)),
      durationControl.valueChanges.pipe(startWith(durationControl.value)),
    ])
      .pipe(
        map(([validFrom, duration]) => [validFrom ? new Date(validFrom) : null, Number(duration)] as [Date | null, number]),
        filter(([validFrom, duration]) => {
          if (!validFrom) {
            return false;
          }

          if (isNaN(duration)) {
            console.error(`Error parsing duration: ${this.formGroup.controls.duration.value}. Expected a number`);
            return false;
          }

          return true;
        }),
        map(([validFrom, duration]) =>
          this.licenseConfigurationService.calculateValidToForType(LicenseType.Vacation, validFrom ?? new Date(), duration)
        )
      )
      .subscribe((validTo) => {
        const validToValue = validTo ? new Date(validTo.getTime() - validTo.getTimezoneOffset() * 60000).toISOString().substring(0, 10) : null;

        const validToControl = this.formGroup.controls.validTo;
        validToControl.setValue(validToValue);
      });

    this.durationOptions$.subscribe((options) => {
      this.formGroup.controls.duration.setValue(options[0]);
    });

    this.latestIssueDate$.subscribe((date) => {
      validFromControl.setValidators([Validators.required, DateValidators.maximum(date), DateValidators.notInPast(false)]);
      validFromControl.updateValueAndValidity();
    });

    this.previousValidityPeriod$.subscribe((validityPeriod?: ValidityPeriod) => {
      if (validityPeriod) {
        validFromControl.addValidators([this.noPeriodOverlap(validityPeriod), this.sameYearExtension(validityPeriod)]);
      }
    });

    this.otherVacationLicensesInSameJurisdiction$.subscribe((fishingLicenses?: FishingLicense[]) => {
      if (fishingLicenses && fishingLicenses?.length > 0) {
        validFromControl.addValidators(this.noRepeatedLicense(fishingLicenses));
      }
    });

    this.validFromErrorMapping$ = this.latestIssueDate$.pipe(
      switchMap((date) =>
        forkJoin([
          this.translateService.get('edit_form.validity_period.error.start_date_max', { date: formatDate(date, 'dd.MM.yyyy', 'de') }),
          this.translateService.get('common.form.error.date.not_in_past'),
          this.translateService.get('edit_form.validity_period.error.no_period_overlap', {
            date: this.previousValidityPeriod()?.validTo ? formatDate(this.previousValidityPeriod()!.validTo!, 'dd.MM.yyyy', 'de') : '',
          }),
          this.translateService.get('edit_form.validity_period.error.same_year', {
            year: this.previousValidityPeriod()?.validTo ? new Date(this.previousValidityPeriod()!.validTo!).getFullYear() : '',
          }),
          this.translateService.get('edit_form.validity_period.error.no_repeated_license'),
        ]).pipe(
          map(([maxError, notInPastError, noPeriodOverlapError, sameYearError, noRepeatedLicenseError]) => ({
            dateMaximum: maxError,
            notInPast: notInPastError,
            noPeriodOverlap: noPeriodOverlapError,
            sameYearExtension: sameYearError,
            noRepeatedLicense: noRepeatedLicenseError,
          }))
        )
      )
    );
  }

  private noPeriodOverlap(previousPeriod: ValidityPeriod): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const newFrom = new Date(control.value);
      const prevTo = new Date(previousPeriod.validTo!);

      if (newFrom <= prevTo) {
        return { noPeriodOverlap: true };
      }

      return null;
    };
  }

  private sameYearExtension(previousPeriod: ValidityPeriod): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value || !previousPeriod.validTo) {
        return null;
      }
      const newFrom = new Date(control.value);
      const prevFrom = new Date(previousPeriod.validFrom);

      if (newFrom.getFullYear() !== prevFrom.getFullYear()) {
        return { sameYearExtension: true };
      }
      return null;
    };
  }

  private noRepeatedLicense(otherVacationLicensesInSameJurisdiction: FishingLicense[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) {
        return null;
      }

      const newFrom = new Date(control.value);

      for (const fishingLicense of otherVacationLicensesInSameJurisdiction) {
        if (fishingLicense.validityPeriods[0]) {
          const otherFrom = new Date(fishingLicense.validityPeriods[0].validFrom!);
          if (newFrom.getFullYear() === otherFrom.getFullYear()) {
            return { noRepeatedLicense: true };
          }
        }
      }

      return null;
    };
  }
}
