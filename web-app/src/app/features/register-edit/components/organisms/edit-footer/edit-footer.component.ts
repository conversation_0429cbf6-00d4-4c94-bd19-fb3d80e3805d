import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, contentChild, input, output } from '@angular/core';
import { FormControl } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';
import { twMerge } from 'tailwind-merge';

import { EditFooterPrimaryActionsDirective } from '@/app/features/register-edit/directives/edit-footer-primary-actions.directive';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { ButtonType } from '@/app/shared/atoms/button/button.models';
import { IconArrowLeftComponent } from '@/app/shared/icons/arrow-left/arrow-left.component';
import { IconArrowRightComponent } from '@/app/shared/icons/arrow-right/arrow-right.component';
import { IconCheckComponent } from '@/app/shared/icons/check/check.component';
import { ConfirmBoxComponent } from '@/app/shared/molecules/confirm-box/confirm-box.component';

@Component({
  selector: 'fish-edit-footer',
  standalone: true,
  imports: [ButtonComponent, ConfirmBoxComponent, TranslateModule, CommonModule, IconArrowRightComponent, IconCheckComponent, IconArrowLeftComponent],
  templateUrl: './edit-footer.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EditFooterComponent {
  // Inputs
  public readonly showGdpr = input<boolean>(false);

  public readonly thirdPartySubmissionControl = input<FormControl>();

  public readonly showBackButton = input<boolean>(false);

  public readonly isLastStep = input<boolean>(false);

  public readonly isLoading = input<boolean>(false);

  public readonly continueButtonLabel = input<string>();

  public readonly continueButtonType = input<ButtonType>('primary');

  // Outputs
  public readonly gdprChanged = output<void>();

  public readonly backed = output<void>();

  public readonly continued = output<void>();

  // Fields
  protected readonly primaryActions = contentChild(EditFooterPrimaryActionsDirective);

  protected get containerClasses(): string {
    // This ensures that the submit button is always on the right, regardless of what is shown on the left side
    return twMerge([
      'w-full flex items-center h-16',
      this.showBackButton() || this.thirdPartySubmissionControl() ? 'justify-between' : 'justify-end',
    ]);
  }
}
