import { ChangeDetectionStrategy, Component, OnInit, inject } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';

import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Observable, combineLatest, map } from 'rxjs';

import { LimitedLicenseSigningEmployeeFormGroup } from '@/app/features/register-edit/components/organisms/limited-license-signing-employee-form/limited-license-signing-employee-form.models';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { DIN91379_REGEXES } from '@/app/shared/models/constants';
import { FormFieldComponent } from '@/app/shared/organisms/form-field/form-field.component';
import { ValidationErrorMapping } from '@/app/shared/organisms/form-field/form-field.models';
import { EmailValidators } from '@/app/shared/validators/email.validators';

@Component({
  selector: 'fish-limited-license-signing-employee-form',
  standalone: true,
  imports: [FormFieldComponent, TranslateModule],
  templateUrl: './limited-license-signing-employee-form.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LimitedLicenseSigningEmployeeFormComponent extends FormComponent<LimitedLicenseSigningEmployeeFormGroup> implements OnInit {
  public override formGroup!: LimitedLicenseSigningEmployeeFormGroup;

  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  protected emailErrorMapping$!: Observable<ValidationErrorMapping>;

  private readonly translate: TranslateService = inject(TranslateService);

  constructor() {
    super();
  }

  public override ngOnInit(): void {
    this.initFormGroup();
    this.captureInitialState();
    this.initErrorMappings();
    super.ngOnInit();
  }

  private initFormGroup(): void {
    this.formGroup = this.formBuilder.group({
      personalSign: this.formBuilder.control('', [Validators.required, Validators.pattern(DIN91379_REGEXES.GROUP_B_GENERAL_NAMES)]),
      name: this.formBuilder.control('', [Validators.required, Validators.pattern(DIN91379_REGEXES.GROUP_A_PERSONS)]),
      email: this.formBuilder.control('', [Validators.required, EmailValidators.strictEmailValidator]),
      phone: this.formBuilder.control('', [Validators.required, Validators.pattern(DIN91379_REGEXES.GROUP_B_GENERAL_NAMES)]),
    }) as LimitedLicenseSigningEmployeeFormGroup;
  }

  private initErrorMappings(): void {
    this.emailErrorMapping$ = combineLatest([this.translate.get('common.form.error.email')]).pipe(
      map(([invalidEmailMessage]) => ({
        email: invalidEmailMessage,
      }))
    );
  }
}
