import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { IdentificationDocument, IdentificationDocumentsMailTemplateType } from '@digifischdok/ngx-register-sdk';

import { DocumentsBoxComponent } from '@/app/features/register-edit/components/organisms/documents-box/documents-box.component';
import { LicenseCardboxComponent } from '@/app/shared/atoms/license-cardbox/license-cardbox.component';

@Component({
  selector: 'fish-documents-step',
  standalone: true,
  imports: [TranslateModule, LicenseCardboxComponent, DocumentsBoxComponent],
  templateUrl: './documents-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentsStepComponent {
  public readonly registerEntryId = input.required<string>();

  public readonly documents = input.required<IdentificationDocument[]>();

  public readonly identificationDocumentsMailTemplateType = input<IdentificationDocumentsMailTemplateType>();

  public readonly showLicenseCardBox = input<boolean>(true);
}
