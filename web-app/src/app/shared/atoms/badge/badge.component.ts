import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

import { twMerge } from 'tailwind-merge';

import { badgeVariants } from '@/app/shared/atoms/badge/badge.component.styles';
import { BadgeType } from '@/app/shared/atoms/badge/badge.models';

@Component({
  selector: 'fish-badge',
  standalone: true,
  imports: [],
  templateUrl: './badge.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BadgeComponent {
  public readonly type = input<BadgeType>('primary');

  protected readonly badgeClasses = computed(() => {
    return twMerge(
      badgeVariants({
        type: this.type(),
      })
    );
  });
}
