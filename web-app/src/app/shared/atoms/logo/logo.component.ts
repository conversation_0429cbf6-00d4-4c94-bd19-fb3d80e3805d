import { ChangeDetectionStrategy, Component, Input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

@Component({
  standalone: true,
  imports: [TranslateModule],
  templateUrl: './logo.component.html',
  selector: 'fish-logo',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LogoComponent {
  @Input() public size: 'm' | 'l' = 'm';

  protected get imagePath(): string {
    return `/assets/logo/logo-${this.size}.svg`;
  }
}
