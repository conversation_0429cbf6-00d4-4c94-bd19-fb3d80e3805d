import { Injectable } from '@angular/core';

import { FishingLicense, LicenseType } from '@digifischdok/ngx-register-sdk';

import { parseDate } from '@/app/shared/utils/date.utils';

@Injectable({
  providedIn: 'root',
})
export class LicenseService {
  public isLicenseExtendable(license: FishingLicense): boolean {
    // Only vacation type license are extendable
    if (license.type !== LicenseType.Vacation) {
      return false;
    }

    if (license.validityPeriods.length === 0) {
      throw new Error('Error while trying to display fishing license: No Validity Periods found.');
    }

    // A license is not extendable if it was already extended.
    if (license.validityPeriods.length > 1) {
      return false;
    }

    const { validFrom, validTo } = license.validityPeriods.at(0)!;

    // If the license is valid indefinitely, it is not extendable
    if (!validTo) {
      return false;
    }

    const validFromDate = parseDate(validFrom);
    const validToDate = parseDate(validTo);

    // If the date ends on the 31st of december (or after) of the starting date year, it is not extendable
    const december30 = new Date(validFromDate.getFullYear(), 11, 30); // in the Date constructor months are zero-indexed
    if (validToDate > december30) {
      return false;
    }

    return true;
  }

  public getNextExtendableLicense(licenses: FishingLicense[]): FishingLicense | undefined {
    // Filter all extendable licenses that aren't expired
    const extendableLicenses = licenses.filter((license) => this.isLicenseExtendable(license));

    if (extendableLicenses.length === 0) {
      return undefined;
    }

    // Sort extendable licenses by their expiration date (earliest first)
    extendableLicenses.sort((a, b) => {
      const validFromA = parseDate(a.validityPeriods[0].validFrom!);
      const validFromB = parseDate(b.validityPeriods[0].validFrom!);
      return validFromA.getTime() - validFromB.getTime();
    });

    // Return the license with the earliest expiration date
    return extendableLicenses[0];
  }

  public isLicenseExpired(license: FishingLicense): boolean {
    // There is some validitiy without an expiration date, so the license is not expired
    if (license.validityPeriods.some((period) => !period.validTo)) {
      return false;
    }

    // Else, return whether all expiration dates are in the past
    // (Note that here all validTo dates have to be defined)
    return license.validityPeriods.every((period) => parseDate(period.validTo!) < new Date());
  }

  /**
   * Returns a boolean value, whether licenses are creatable.
   * @param licenses
   */
  public isAnyLicenseCreatable(licenses: FishingLicense[]): boolean {
    return licenses.every((license) => license.type !== LicenseType.Regular);
  }

  /**
   * Filters all licenses so only relevant licenses are kept.
   *
   * Rules for relevance:
   *
   * 1. Regular licenses are ALWAYS relevant
   *
   * 2. Of limited licenses only the latest is relevant (even when it is in the past!)
   *
   * 3. Of Vacation licenses only licenses from this year or future licenses are relevant
   * @param licenses
   */
  public getRelevantLicenses(licenses: FishingLicense[]): FishingLicense[] {
    const latestLimitedLicense = this.getLatestLimitedLicense(licenses);

    return licenses.filter((license) => this.isLicensesRelevant(license, latestLimitedLicense));
  }

  private isLicensesRelevant(license: FishingLicense, latestLimitedLicense: FishingLicense | undefined) {
    // Always show regular license
    if (license.type === LicenseType.Regular) {
      return true;
    }

    // Only show latest special license
    if (license.type === LicenseType.Limited) {
      return license === latestLimitedLicense;
    }

    if (license.type === LicenseType.Vacation) {
      // Always show indefinite vacation licenses
      if (license.validityPeriods.some((period) => period.validTo === null)) {
        return true;
      }

      // Show only if at least one license expires the current year or after
      const currentYear = new Date().getFullYear();
      return license.validityPeriods.some((period) => parseDate(period.validTo!) > new Date(currentYear - 1, 11, 31));
    }

    throw new Error('Unsupported license type');
  }

  private getLatestLimitedLicense(licenses: FishingLicense[]): FishingLicense | undefined {
    // Filter and sort in one chain
    const limitedLicenses = licenses.filter((license) => license.type === LicenseType.Limited).sort(this.validToCompareFn);

    // Return the first license (latest) or undefined if no limited licenses exist
    return limitedLicenses.at(0);
  }

  private validToCompareFn(a: FishingLicense, b: FishingLicense): number {
    const validToA = this.getValidToFromLicense(a);
    const validToB = this.getValidToFromLicense(b);

    // Handle undefined dates
    if (!validToA && !validToB) {
      return 0;
    }

    // Place licenses without validTo last
    if (!validToA) {
      return 1;
    }
    if (!validToB) {
      return -1;
    }

    // Compare parsed dates
    return parseDate(validToB).getTime() - parseDate(validToA).getTime();
  }

  /**
   * Shorthand for getting the validTo from a license containing only 1 validity period.
   * @private
   */
  private getValidToFromLicense(license: FishingLicense) {
    return license.validityPeriods[0].validTo;
  }
}
