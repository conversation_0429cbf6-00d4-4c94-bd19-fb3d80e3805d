import { AnimationEvent } from '@angular/animations';
import { Dialog, DialogRef } from '@angular/cdk/dialog';
import { OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  EventEmitter,
  Output,
  TemplateRef,
  ViewChild,
  signal,
} from '@angular/core';

import { openCloseAnimation } from '@/app/shared/animations/open-close.animations';

import { DialogHeaderComponent } from '../dialog-header/dialog-header.component';

@Component({
  selector: 'fish-dialog',
  standalone: true,
  imports: [OverlayModule, CommonModule, DialogHeaderComponent],
  templateUrl: './dialog.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [openCloseAnimation],
})
export class DialogComponent implements AfterContentInit {
  @Output() public readonly closed = new EventEmitter<void>();

  protected readonly isOpen = signal(false);

  @ViewChild('dialogContent')
  private readonly dialogContent!: TemplateRef<unknown>;

  @ContentChild(DialogHeaderComponent)
  private readonly dialogHeaderComponent!: DialogHeaderComponent;

  private dialogRef: DialogRef<DialogRef> | null = null;

  constructor(private readonly dialog: Dialog) {}

  public ngAfterContentInit(): void {
    this.dialogHeaderComponent.closed.subscribe(() => this.closeDialog());
  }

  public openDialog(): void {
    this.dialogRef = this.dialog.open(this.dialogContent, {
      maxWidth: '100vw',
      maxHeight: '100vh',
      height: '100%',
      width: '100%',
      panelClass: ['transition-all', 'duration-240', 'bg-background-overlay', 'backdrop-blur-md'],
      autoFocus: 'first-tabbable',
      hasBackdrop: false,
      disableClose: true,
    });
    this.isOpen.set(true);
    this.dialog.afterAllClosed.subscribe(() => this.closed.emit());
  }

  public closeDialog(): void {
    this.isOpen.set(false);
    this.dialogRef?.removePanelClass(['bg-background-overlay', 'backdrop-blur-md']);
  }

  protected onAnimationDone(event: AnimationEvent): void {
    // Close Dialog after animation has finnished
    if (event.toState === 'close') {
      this.dialogRef?.close();
    }
  }
}
