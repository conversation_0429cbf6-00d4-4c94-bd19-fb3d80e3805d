import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { RouterLink } from '@angular/router';

import { FocusRingComponent } from '@/app/shared/atoms/focus-ring/focus-ring.component';

import { InspectionLinkComponent } from '../../atoms/inspection-link/inspection-link.component';

@Component({
  selector: 'fish-task',
  standalone: true,
  imports: [InspectionLinkComponent, RouterLink, FocusRingComponent],
  templateUrl: './task.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TaskComponent {
  // INPUTS
  public readonly link = input.required<string | string[]>();
}
