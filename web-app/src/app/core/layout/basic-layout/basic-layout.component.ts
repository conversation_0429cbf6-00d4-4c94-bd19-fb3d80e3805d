import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';

import { BackgroundGradientComponent } from '@/app/core/layout/background-gradient/background-gradient.component';
import { BackgroundGraphicComponent } from '@/app/core/layout/background-graphic/background-graphic.component';
import { HeaderComponent } from '@/app/core/layout/header/header.component';

@Component({
  selector: 'fish-basic-layout',
  standalone: true,
  imports: [BackgroundGradientComponent, BackgroundGraphicComponent, HeaderComponent, RouterOutlet],
  templateUrl: './basic-layout.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BasicLayoutComponent {}
