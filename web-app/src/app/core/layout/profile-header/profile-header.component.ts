import { ChangeDetectionStrategy, Component, Input, Signal, computed, inject } from '@angular/core';
import { Router, RouterLink } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';

import { CitizenStore } from '@/app/core/stores/citizen.store';
import { ProfileHeaderStore } from '@/app/core/stores/profile-header.store';
import { SearchHistoryStore } from '@/app/core/stores/search-history.store';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { ButtonType } from '@/app/shared/atoms/button/button.models';
import { IconGridComponent } from '@/app/shared/icons/grid/grid.component';
import { IconHomeComponent } from '@/app/shared/icons/home/<USER>';
import { IconReturnComponent } from '@/app/shared/icons/return/return.component';
import { PersonFormatterService } from '@/app/shared/services/person-formatter.service';

@Component({
  selector: 'fish-profile-header',
  standalone: true,
  imports: [ButtonComponent, TranslateModule, RouterLink, IconGridComponent, IconHomeComponent, IconReturnComponent],
  templateUrl: './profile-header.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ProfileHeaderComponent {
  // Inputs
  @Input() public text?: string;

  // Fields
  protected readonly displayedText: Signal<string> = computed(() => {
    const person = this.citizenStore.profile()?.person;

    const citizenFullname = person ? this.personFormatter.formatName(person) : '';

    return this.text || citizenFullname || this.profileHeaderStore.text() || '—';
  });

  protected readonly homeButtonType: Signal<ButtonType> = computed(() => {
    return this.profileHeaderStore.homeButtonType();
  });

  protected readonly showServiceOverviewButton: Signal<boolean> = computed(() => {
    return this.profileHeaderStore.showServiceOverviewButton();
  });

  protected readonly registerEntryId: Signal<string | undefined> = computed(() => {
    return this.citizenStore.profile()?.registerId;
  });

  protected readonly showBackToSearchResultsButton: Signal<boolean> = computed(() => {
    return this.searchHistoryStore.isFromSearchPage();
  });

  // Dependencies
  private readonly router = inject(Router);

  private readonly personFormatter = inject(PersonFormatterService);

  private readonly profileHeaderStore = inject(ProfileHeaderStore);

  private readonly citizenStore = inject(CitizenStore);

  private readonly searchHistoryStore = inject(SearchHistoryStore);

  protected handleServiceOverviewButtonClicked(): void {
    this.profileHeaderStore.setHomeButtonType('secondary');

    const registerEntryId = this.registerEntryId();
    if (!registerEntryId) {
      throw new Error('Error while trying to access service overview: Registerentry ID is missing.');
    }

    this.router.navigate(['register-entries', this.registerEntryId()]).catch((err) => {
      console.error('Navigation error:', err);
    });
  }

  protected handleBackToSearchButtonClicked(): void {
    const searchQuery = this.searchHistoryStore.lastSearchQuery();

    if (searchQuery) {
      this.searchHistoryStore.setFromSearchPage(false);
      this.router.navigate(['register-entries'], { queryParams: { search: searchQuery } }).catch((err) => {
        console.error('Navigation error:', err);
      });
    }
  }
}
